class Shift {
  DateTime startTime;
  DateTime endTime;
  String? skill;

  Shift({
    required this.startTime,
    required this.endTime,
    this.skill,
  });
}

class ScheduleEntry {
  final String employeeId;
  final String employeeName;
  final List<Shift?> shifts;
  final String? skill;

  ScheduleEntry({
    required this.employeeId,
    required this.employeeName,
    required this.shifts,
    this.skill,
  });
}