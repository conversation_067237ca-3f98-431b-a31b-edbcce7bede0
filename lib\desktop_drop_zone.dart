import 'package:desktop_drop/desktop_drop.dart';
import 'package:flutter/material.dart';
import 'platform_drop_zone.dart';

class DesktopDropZone extends StatefulWidget {
  final Widget child;
  final void Function(List<int> bytes) onDrop;

  const DesktopDropZone({
    Key? key,
    required this.child,
    required this.onDrop,
  }) : super(key: key);

  @override
  _DesktopDropZoneState createState() => _DesktopDropZoneState();
}

class _DesktopDropZoneState extends State<DesktopDropZone> {
  bool _highlighted = false;

  @override
  Widget build(BuildContext context) {
    return DropTarget(
      onDragDone: (detail) async {
        final file = detail.files.first;
        final bytes = await file.readAsBytes();
        widget.onDrop(bytes);
      },
      onDragEntered: (detail) {
        setState(() {
          _highlighted = true;
        });
      },
      onDragExited: (detail) {
        setState(() {
          _highlighted = false;
        });
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: _highlighted ? Colors.blue : Colors.transparent,
            width: 4,
          ),
        ),
        child: widget.child,
      ),
    );
  }
}

PlatformDropZone getPlatformDropZone({
  required Widget child,
  required void Function(List<int> bytes) onDrop,
}) =>
    PlatformDropZone(
      child: DesktopDropZone(
        onDrop: onDrop,
        child: child,
      ),
      onDrop: onDrop,
    );