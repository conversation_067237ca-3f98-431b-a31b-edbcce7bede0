import 'dart:convert';
import 'dart:html' as html;
import 'dart:typed_data';
import 'saver_stub.dart';

class WebSaver implements FileSaver {
  @override
  Future<void> save(Uint8List bytes, String fileName) async {
    final base64 = base64Encode(bytes);
    final anchor = html.AnchorElement(href: 'data:application/octet-stream;base64,$base64')
      ..setAttribute('download', fileName)
      ..click();
  }
}

FileSaver getSaver() => WebSaver();