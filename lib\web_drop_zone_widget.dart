import 'dart:html' as html;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'web_drop_zone_widget_stub.dart';

class WebDropZoneWidget extends StatefulWidget {
  final Widget child;
  final Future<void> Function(Uint8List bytes) onFileDropped;

  const WebDropZoneWidget({
    Key? key,
    required this.child,
    required this.onFileDropped,
  }) : super(key: key);

  @override
  _WebDropZoneWidgetState createState() => _WebDropZoneWidgetState();
}

class _WebDropZoneWidgetState extends State<WebDropZoneWidget> {
  bool _highlighted = false;

  @override
  void initState() {
    super.initState();
    html.document.body!.onDragOver.listen((event) {
      event.preventDefault();
      setState(() {
        _highlighted = true;
      });
    });
    html.document.body!.onDragLeave.listen((event) {
      event.preventDefault();
      setState(() {
        _highlighted = false;
      });
    });
    html.document.body!.onDrop.listen((event) {
      event.preventDefault();
      setState(() {
        _highlighted = false;
      });
      final files = event.dataTransfer.files;
      if (files != null && files.isNotEmpty) {
        final file = files.first;
        final reader = html.FileReader();
        reader.readAsArrayBuffer(file);
        reader.onLoadEnd.listen((event) {
          widget.onFileDropped(reader.result as Uint8List);
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: _highlighted ? Colors.blue : Colors.transparent,
          width: 4,
        ),
      ),
      child: widget.child,
    );
  }
}