import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart' hide Border;
import 'package:xml/xml.dart';
import 'package:intl/intl.dart';
import 'package:super_drag_and_drop/super_drag_and_drop.dart';
import 'saver_stub.dart' if (dart.library.io) 'desktop_saver.dart' if (dart.library.html) 'web_saver.dart';
import 'web_drop_zone_widget_stub.dart' if (dart.library.html) 'web_drop_zone_widget.dart';
import 'schedule_entry.dart';

({List<ScheduleEntry> schedules, DateTime startDate, List<ScheduleError> errors}) _parseExcelData(
    Uint8List fileBytes) {
  var excel = Excel.decodeBytes(fileBytes);
  var sheet = excel.tables['ROSTER'];
  final errors = <ScheduleError>[];

  final startDateCell = sheet?.cell(CellIndex.indexByString('C1'));
  if (startDateCell == null || startDateCell.value == null) {
    // Not a schedule error, but a file format error
    throw Exception('Start date not found in C1');
  }

  final dateValue = startDateCell.value.toString();
  final startDate = DateFormat('yyyy-MM-dd').parse(dateValue);

  final newSchedules = <ScheduleEntry>[];

  for (var (rowIndex, row) in sheet!.rows.skip(2).indexed) {
    final skill = row[2]?.value.toString();
    final employeeId = row[3]?.value.toString();
    final employeeName = row[4]?.value.toString();
    if (employeeId == null || employeeId.isEmpty) {
      break;
    }

    if (employeeId.length != 6 || int.tryParse(employeeId) == null) {
      var name = employeeName ?? '';
      if (name == 'null') {
        name = '';
      }
      errors.add(InvalidEmployeeIdError(employeeId, name));
      continue;
    }

    final shifts = <Shift?>[];
    for (int i = 5; i <= 11; i++) {
      final cell = row[i];
      if (cell == null || cell.value == null) {
        shifts.add(null);
        continue;
      }
      final cellValue = cell.value.toString();
      if (cellValue.isEmpty) {
        shifts.add(null);
        continue;
      }

      final dayOffset = i - 5;
      final scheduleDate = startDate.add(Duration(days: dayOffset));

      final shiftPattern = RegExp(r'^(\d{1,4})-(\d{1,4})(.*)$');
      final match = shiftPattern.firstMatch(cellValue);

      if (match == null) {
        shifts.add(null);
        continue;
      }

      final startTimeStr = match.group(1)!.padLeft(4, '0');
      final endTimeStr = match.group(2)!.padLeft(4, '0');
      final shiftSkill = match.group(3)!.trim();

      final startHour = int.parse(startTimeStr.substring(0, 2));
      final startMinute = int.parse(startTimeStr.substring(2, 4));
      final endHour = int.parse(endTimeStr.substring(0, 2));
      final endMinute = int.parse(endTimeStr.substring(2, 4));

      final scheduleStartTime =
          DateTime(scheduleDate.year, scheduleDate.month, scheduleDate.day, startHour, startMinute);
      var scheduleEndTime =
          DateTime(scheduleDate.year, scheduleDate.month, scheduleDate.day, endHour, endMinute);
      if (scheduleEndTime.isBefore(scheduleStartTime)) {
        scheduleEndTime = scheduleEndTime.add(const Duration(days: 1));
      }

      shifts.add(
        Shift(
          startTime: scheduleStartTime,
          endTime: scheduleEndTime,
          skill: shiftSkill.isNotEmpty ? shiftSkill : null,
        ),
      );
    }
    newSchedules.add(
      ScheduleEntry(
        employeeId: employeeId,
        employeeName: employeeName ?? '',
        shifts: shifts,
        skill: skill,
      ),
    );
  }
  return (schedules: newSchedules, startDate: startDate, errors: errors);
}

String _generateXml(({List<ScheduleEntry> schedules, DateTime startDate}) params) {
  final schedules = params.schedules;
  final importStartDate = params.startDate;

  final builder = XmlBuilder();
  builder.processing('xml', 'version="1.0" encoding="UTF-16"');
  builder.element('EmployeeScheduleImport', nest: () {
    builder.element('ScheduleImport', nest: () {
      final endDate = importStartDate.add(const Duration(days: 8)).subtract(const Duration(minutes: 1));
      builder.element('StartTime',
          nest: () => builder.text(DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").format(importStartDate)));
      builder.element('EndTime',
          nest: () => builder.text(DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").format(endDate)));
      builder.element('DeleteLevel', nest: () => builder.text('ALL'));
      builder.element('ValidationLevel', nest: () => builder.text('NONE'));

      for (final schedule in schedules) {
        for (final shift in schedule.shifts) {
          if (shift == null) continue;

          builder.element('EmployeeSchedule', nest: () {
            builder.element('EmployeeXrefCode', nest: () => builder.text(schedule.employeeId));
            builder.element('StartTime',
                nest: () =>
                    builder.text(DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").format(shift.startTime)));
            builder.element('EndTime',
                nest: () =>
                    builder.text(DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").format(shift.endTime)));
            if (schedule.skill != null) {
              builder.element('SkillQualifier', nest: () {
                builder.element('SkillXRefCode', nest: () => builder.text(schedule.skill!));
                builder.element('IsMandatory', nest: () => builder.text('1'));
              });
            }
            if (shift.skill != null) {
              builder.element('LaborMetrics', nest: () {
                builder.element('LaborMetricsTypeXRefCode', nest: () => builder.text('TeamPlayer'));
                builder.element('LaborMetricsCodeXRefCode', nest: () => builder.text(shift.skill!));
              });
            }
          });
        }
      }
    });
  });

  final xmlDocument = builder.buildDocument();
  return xmlDocument.toXmlString(pretty: true);
}

List<OverlapInfo> _findOverlappingShiftPairsIsolate(List<ScheduleEntry> schedules) {
  final overlappingPairs = <OverlapInfo>[];
  final employeeSchedules = <String, List<Shift>>{};
  final employeeData = <String, ScheduleEntry>{};

  for (final schedule in schedules) {
    final employeeId = schedule.employeeId;
    employeeData[employeeId] = schedule;
    final shifts = schedule.shifts.whereType<Shift>().toList();
    if (employeeSchedules.containsKey(employeeId)) {
      employeeSchedules[employeeId]!.addAll(shifts);
    } else {
      employeeSchedules[employeeId] = shifts;
    }
  }

  for (final employeeId in employeeSchedules.keys) {
    final allShiftsForEmployee = employeeSchedules[employeeId]!;
    allShiftsForEmployee.sort((a, b) => a.startTime.compareTo(b.startTime));
    for (int i = 0; i < allShiftsForEmployee.length; i++) {
      for (int j = i + 1; j < allShiftsForEmployee.length; j++) {
        final shift1 = allShiftsForEmployee[i];
        final shift2 = allShiftsForEmployee[j];

        if (shift1.startTime.isBefore(shift2.endTime) && shift2.startTime.isBefore(shift1.endTime)) {
          final employee = employeeData[employeeId]!;
          overlappingPairs.add(OverlapInfo(employee.employeeId, employee.employeeName, shift1, shift2));
        }
      }
    }
  }
  return overlappingPairs;
}


class OverlapInfo {
  final String employeeId;
  final String employeeName;
  final Shift shift1;
  final Shift shift2;

  OverlapInfo(this.employeeId, this.employeeName, this.shift1, this.shift2);
}

abstract class ScheduleError {
  final String employeeId;
  final String employeeName;

  ScheduleError(this.employeeId, this.employeeName);
}

class OverlapError extends ScheduleError {
  final Shift shift1;
  final Shift shift2;

  OverlapError(String employeeId, String employeeName, this.shift1, this.shift2)
      : super(employeeId, employeeName);
}

class InvalidShiftFormatError extends ScheduleError {
  final String cell;
  final String value;

  InvalidShiftFormatError(String employeeId, String employeeName, this.cell, this.value)
      : super(employeeId, employeeName);
}

class InvalidEmployeeIdError extends ScheduleError {
  InvalidEmployeeIdError(String employeeId, String employeeName) : super(employeeId, employeeName);
}

void main() {
  runApp(const MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData(
        primarySwatch: Colors.blue,
        primaryColor: Colors.blue,
        colorScheme: ColorScheme.fromSwatch(primarySwatch: Colors.blue).copyWith(secondary: Colors.blue),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
      ),
      home: const HomePage(),
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final _fileSaver = getSaver();
  bool _dragging = false;
  bool _isLoading = false;
  List<ScheduleEntry> _schedules = [];
  List<ScheduleError> _scheduleErrors = [];
  DateTime? _scheduleStartDate;
  
  Future<void> _showFixErrorsDialog() async {
    if (!mounted) return;

    final errorsToShow = List<ScheduleError>.from(_scheduleErrors);
    _scheduleErrors.clear();

    for (final error in errorsToShow) {
      if (error is OverlapError) {
        final date = DateFormat.yMMMd().format(error.shift1.startTime);
        final day = DateFormat.EEEE().format(error.shift1.startTime);
        final fixOption = await showDialog<String>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            final title = error.employeeName.isNotEmpty
                ? 'Overlap Error for ${error.employeeName} (${error.employeeId})'
                : 'Overlap Error for Employee ${error.employeeId}';
            return AlertDialog(
              title: Text(title),
              content: Text('On $day, $date, the following shifts overlap:\n\n'
                  'Shift 1: ${DateFormat.Hm().format(error.shift1.startTime)} - ${DateFormat.Hm().format(error.shift1.endTime)}\n'
                  'Shift 2: ${DateFormat.Hm().format(error.shift2.startTime)} - ${DateFormat.Hm().format(error.shift2.endTime)}\n\n'
                  'How would you like to resolve this?'),
              actions: <Widget>[
                TextButton(
                  child: const Text('Merge Shifts'),
                  onPressed: () => Navigator.of(context).pop('merge'),
                ),
                TextButton(
                  child: const Text('Adjust End Time (1st Shift)'),
                  onPressed: () => Navigator.of(context).pop('adjust_end'),
                ),
                TextButton(
                  child: const Text('Adjust Start Time (2nd Shift)'),
                  onPressed: () => Navigator.of(context).pop('adjust_start'),
                ),
                TextButton(
                  child: const Text('Edit Shift 1'),
                  onPressed: () => Navigator.of(context).pop('edit1'),
                ),
                TextButton(
                  child: const Text('Edit Shift 2'),
                  onPressed: () => Navigator.of(context).pop('edit2'),
                ),
                TextButton(
                  child: const Text('Delete Shift 1'),
                  onPressed: () => Navigator.of(context).pop('delete1'),
                ),
                TextButton(
                  child: const Text('Delete Shift 2'),
                  onPressed: () => Navigator.of(context).pop('delete2'),
                ),
              ],
            );
          },
        );

        if (fixOption == 'merge') {
          setState(() {
            final mergedShift = Shift(
                startTime: error.shift1.startTime,
                endTime: error.shift2.endTime,
                skill: error.shift1.skill ?? error.shift2.skill);
            for (final schedule in _schedules) {
              if (schedule.employeeId == error.employeeId) {
                final index1 = schedule.shifts.indexOf(error.shift1);
                if (index1 != -1) {
                  schedule.shifts[index1] = mergedShift;
                }
                final index2 = schedule.shifts.indexOf(error.shift2);
                if (index2 != -1) {
                  schedule.shifts.removeAt(index2);
                }
              }
            }
          });
        } else if (fixOption == 'adjust_start') {
          setState(() {
            error.shift2.startTime = error.shift1.endTime;
          });
        } else if (fixOption == 'adjust_end') {
          setState(() {
            error.shift1.endTime = error.shift2.startTime;
          });
        } else if (fixOption == 'edit1') {
          final editedShift = await _showEditShiftDialog(error, date: error.shift1.startTime);
          if (editedShift != null) {
            setState(() {
              error.shift1.startTime = editedShift.startTime;
              error.shift1.endTime = editedShift.endTime;
              error.shift1.skill = editedShift.skill;
            });
          }
        } else if (fixOption == 'edit2') {
          final editedShift = await _showEditShiftDialog(error, date: error.shift2.startTime);
          if (editedShift != null) {
            setState(() {
              error.shift2.startTime = editedShift.startTime;
              error.shift2.endTime = editedShift.endTime;
              error.shift2.skill = editedShift.skill;
            });
          }
        } else if (fixOption == 'delete1') {
          setState(() {
            for (final schedule in _schedules) {
              if (schedule.employeeId == error.employeeId) {
                final index = schedule.shifts.indexOf(error.shift1);
                if (index != -1) {
                  schedule.shifts[index] = null;
                }
              }
            }
          });
        } else if (fixOption == 'delete2') {
          setState(() {
            for (final schedule in _schedules) {
              if (schedule.employeeId == error.employeeId) {
                final index = schedule.shifts.indexOf(error.shift2);
                if (index != -1) {
                  schedule.shifts[index] = null;
                }
              }
            }
          });
        }
      } else if (error is InvalidShiftFormatError) {
        final dayIndex = error.cell.codeUnitAt(0) - 'F'.codeUnitAt(0);
        final date = _scheduleStartDate!.add(Duration(days: dayIndex));
        final day = DateFormat.EEEE().format(date);
        final editedShift = await _showEditShiftDialog(error, initialValue: error.value, date: date);
        if (editedShift != null) {
          setState(() {
            for (final schedule in _schedules) {
              if (schedule.employeeId == error.employeeId) {
                final col = error.cell.codeUnitAt(0) - 'A'.codeUnitAt(0);
                final dayIndex = col - 5;
                if (dayIndex >= 0 && dayIndex < schedule.shifts.length) {
                  schedule.shifts[dayIndex] = editedShift;
                }
              }
            }
          });
        }
      } else if (error is InvalidEmployeeIdError) {
        final title = error.employeeName.isNotEmpty
            ? 'Invalid Employee ID for ${error.employeeName}'
            : 'Invalid Employee ID';
        await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(title),
            content: Text('The employee ID "${error.employeeId}" is not a valid 6-digit number.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    }
    await _findScheduleErrors();
  }

  Future<Shift?> _showEditShiftDialog(ScheduleError error, {String? initialValue, DateTime? date}) async {
    final _shiftController = TextEditingController(
        text: initialValue ??
            (error is OverlapError
                ? '${DateFormat.Hm().format(error.shift1.startTime)}-${DateFormat.Hm().format(error.shift1.endTime)}${error.shift1.skill ?? ''}'
                : ''));

    return showDialog<Shift>(
      context: context,
      builder: (context) {
        final title =
            error.employeeName.isNotEmpty ? 'Edit Shift for ${error.employeeName}' : 'Edit Shift';
        return AlertDialog(
          title: Text(title),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (date != null) Text(DateFormat.yMMMd().format(date)),
              TextField(
                controller: _shiftController,
                decoration: const InputDecoration(labelText: 'Shift (HHmm-HHmm) or (HHmm-HHmmSKILL)'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                final shiftText = _shiftController.text;
                final shiftPattern = RegExp(r'^(\d{1,4})-(\d{1,4})(.*)$');
                final match = shiftPattern.firstMatch(shiftText);
                if (match == null) {
                  // Show an error to the user
                  return;
                }
                final startTimeStr = match.group(1)!.padLeft(4, '0');
                final endTimeStr = match.group(2)!.padLeft(4, '0');
                final skill = match.group(3)!.trim();

                final startHour = int.parse(startTimeStr.substring(0, 2));
                final startMinute = int.parse(startTimeStr.substring(2, 4));
                final endHour = int.parse(endTimeStr.substring(0, 2));
                final endMinute = int.parse(endTimeStr.substring(2, 4));

                final shiftDate = date ?? (error as OverlapError).shift1.startTime;

                final newStartTime =
                    DateTime(shiftDate.year, shiftDate.month, shiftDate.day, startHour, startMinute);
                var newEndTime =
                    DateTime(shiftDate.year, shiftDate.month, shiftDate.day, endHour, endMinute);
                if (newEndTime.isBefore(newStartTime)) {
                  newEndTime = newEndTime.add(const Duration(days: 1));
                }

                Navigator.of(context).pop(Shift(
                    startTime: newStartTime,
                    endTime: newEndTime,
                    skill: skill.isNotEmpty ? skill : null));
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _findScheduleErrors() async {
    final overlaps = await compute(_findOverlappingShiftPairsIsolate, _schedules);
    final overlapErrors =
        overlaps.map((o) => OverlapError(o.employeeId, o.employeeName, o.shift1, o.shift2));
    setState(() {
      _scheduleErrors.addAll(overlapErrors);
    });
  }

  Future<void> _processFile(Uint8List fileBytes) async {
    setState(() {
      _isLoading = true;
    });
    try {
      final result = await compute(_parseExcelData, fileBytes);
      if (mounted) {
        setState(() {
          _schedules = result.schedules;
          _scheduleStartDate = result.startDate;
          _scheduleErrors = result.errors;
        });
        await _findScheduleErrors(); // This will now find overlaps and add to the list
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('An error occurred: $e'),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _saveFile() async {
    if (_scheduleErrors.isNotEmpty) {
      final continueSave = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Unresolved Errors'),
          content: const Text(
              'There are still unresolved errors in the schedule. Are you sure you want to save the XML file?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Save Anyway'),
            ),
          ],
        ),
      );
      if (continueSave != true) {
        return;
      }
    }
    if (_schedules.isNotEmpty) {
      try {
        final params = (schedules: _schedules, startDate: _scheduleStartDate!);
        final xmlString = await compute(_generateXml, params);
        final bytes = utf8.encode(xmlString);
        await _fileSaver.save(bytes, 'employee_schedule.xml');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('XML file saved successfully!'),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('An error occurred while saving: $e'),
            ),
          );
        }
      }
    }
  }

  Future<void> _pickFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['xlsx', 'xlsm'],
      withData: true,
    );

    if (result != null) {
      await _processFile(result.files.first.bytes!);
    }
  }

  void _removeAllShiftSkills() {
    setState(() {
      for (final schedule in _schedules) {
        for (final shift in schedule.shifts) {
          shift?.skill = null;
        }
      }
    });
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    String titleText = 'Dayforce XML Generator';
    if (_scheduleStartDate != null) {
      final endDate = _scheduleStartDate!.add(const Duration(days: 6));
      final format = DateFormat('MMM d');
      titleText = 'Week of ${format.format(_scheduleStartDate!)} - ${format.format(endDate)}';
    }

    final child = _schedules.isEmpty
        ? Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _pickFile,
                  child: const Text('Open an Excel File'),
                ),
                const SizedBox(height: 20),
                const Text('or drag and drop a file here'),
              ],
            ),
          )
        : Center(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SizedBox(
                width: 1150,
                child: Column(
                  children: [
                    // Header Row
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border(
                          bottom: BorderSide(
                            color: Theme.of(context).dividerColor,
                            width: 2.0,
                          ),
                        ),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: const Row(
                        children: [
                          SizedBox(
                              width: 150,
                              child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Padding(
                                    padding: const EdgeInsets.only(left: 8.0),
                                    child: Text('Employee ID'),
                                  ))),
                          SizedBox(
                              width: 200,
                              child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Padding(
                                    padding: const EdgeInsets.only(left: 8.0),
                                    child: Text('Employee Name'),
                                  ))),
                          SizedBox(
                              width: 100,
                              child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Padding(
                                    padding: const EdgeInsets.only(left: 8.0),
                                    child: Text('Qualification'),
                                  ))),
                          SizedBox(width: 100, child: Center(child: Text('Friday'))),
                          SizedBox(width: 100, child: Center(child: Text('Saturday'))),
                          SizedBox(width: 100, child: Center(child: Text('Sunday'))),
                          SizedBox(width: 100, child: Center(child: Text('Monday'))),
                          SizedBox(width: 100, child: Center(child: Text('Tuesday'))),
                          SizedBox(width: 100, child: Center(child: Text('Wednesday'))),
                          SizedBox(width: 100, child: Center(child: Text('Thursday'))),
                        ],
                      ),
                    ),
                    // Data Rows
                    Expanded(
                      child: ListView.builder(
                        itemExtent: 48.0,
                        itemCount: _schedules.length,
                        itemBuilder: (context, index) {
                          final schedule = _schedules[index];
                          return Container(
                            decoration: BoxDecoration(
                              border: Border(bottom: BorderSide(color: Theme.of(context).dividerColor)),
                            ),
                            child: Row(
                              children: [
                                SizedBox(
                                    width: 150,
                                    child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Padding(
                                          padding: const EdgeInsets.only(left: 8.0),
                                          child: Text(schedule.employeeId),
                                        ))),
                                SizedBox(
                                    width: 200,
                                    child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Padding(
                                          padding: const EdgeInsets.only(left: 8.0),
                                          child: Text(schedule.employeeName),
                                        ))),
                                SizedBox(
                                    width: 100,
                                    child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Padding(
                                          padding: const EdgeInsets.only(left: 8.0),
                                          child: Text(schedule.skill ?? ''),
                                        ))),
                                ...schedule.shifts.map((shift) {
                                  return SizedBox(
                                    width: 100,
                                    child: Container(
                                      color: _scheduleErrors.any((error) =>
                                              error is OverlapError &&
                                              (error.shift1 == shift || error.shift2 == shift))
                                          ? Colors.yellow
                                          : null,
                                      child: Center(
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Text(shift == null
                                                ? ''
                                                : '${DateFormat.Hm().format(shift.startTime)}-${DateFormat.Hm().format(shift.endTime)}'),
                                            if (shift?.skill != null)
                                              Text(shift!.skill!, style: const TextStyle(fontSize: 10)),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );

    final body = Column(
      children: [
        Expanded(
          child: kIsWeb
              ? WebDropZoneWidget(
                 onFileDropped: (fileBytes) async {
                   setState(() {
                     _isLoading = true;
                   });
                   try {
                     await _processFile(fileBytes);
                   } finally {
                     if (mounted) {
                       setState(() {
                         _isLoading = false;
                       });
                     }
                   }
                 },
                 child: child,
               )
              : DropRegion(
                  formats: Formats.standardFormats,
                  onDropOver: (event) {
                    setState(() {
                      _dragging = true;
                    });
                    return DropOperation.copy;
                  },
                  onDropLeave: (event) {
                    setState(() {
                      _dragging = false;
                    });
                  },
                  onPerformDrop: (event) async {
                    setState(() {
                      _dragging = false;
                      _isLoading = true;
                    });
                    await Future.delayed(const Duration(milliseconds: 50));
                    try {
                      final item = event.session.items.first;
                      if (item.canProvide(Formats.fileUri)) {
                        final reader = item.dataReader!;
                        reader.getValue<Uri>(Formats.fileUri, (uri) async {
                          if (uri != null) {
                            try {
                              final file = File.fromUri(uri);
                              final bytes = await file.readAsBytes();
                              await _processFile(bytes);
                            } catch (e) {
                              _showError('Error reading dropped file: $e');
                            }
                          }
                        }, onError: (error) {
                          _showError('Error accessing dropped file: $error');
                        });
                      } else {
                        _showError('Dropped item is not a supported file format. Please drop an Excel file (.xlsx or .xlsm).');
                      }
                    } catch (e) {
                      _showError('Error processing dropped item: $e');
                    }
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: _dragging ? Theme.of(context).primaryColor : Colors.transparent,
                        width: 4,
                      ),
                    ),
                    child: child,
                  ),
                ),
        ),
      ],
    );

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(titleText),
        centerTitle: true,
        actions: [
          if (_schedules.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: TextButton(
                onPressed: _removeAllShiftSkills,
                child: const Text(
                  'Remove All Team Player',
                  style: TextStyle(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: TextButton(
              onPressed: _scheduleErrors.isNotEmpty ? _showFixErrorsDialog : null,
              child: Text(
                'Fix Errors',
                style: TextStyle(
                  color: _scheduleErrors.isNotEmpty ? Colors.white : Colors.grey,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          body,
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.5),
              child: const Center(child: CircularProgressIndicator()),
            ),
        ],
      ),
      floatingActionButton: _schedules.isNotEmpty
          ? FloatingActionButton(
              onPressed: _saveFile,
              child: const Icon(Icons.save),
            )
          : null,
    );
  }
}
