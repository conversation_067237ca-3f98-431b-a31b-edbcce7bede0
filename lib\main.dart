import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart' hide Border;
import 'package:xml/xml.dart';
import 'package:intl/intl.dart';
import 'package:super_drag_and_drop/super_drag_and_drop.dart';
import 'saver_stub.dart' if (dart.library.io) 'desktop_saver.dart' if (dart.library.html) 'web_saver.dart';

void main() {
  runApp(const MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      home: HomePage(),
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final _fileSaver = getSaver();
  bool _dragging = false;

  Future<void> _processFile(Uint8List fileBytes) async {
    try {
      var excel = Excel.decodeBytes(fileBytes);
      var sheet = excel.tables['ROSTER'];

      final startDateCell = sheet?.cell(CellIndex.indexByString('C1'));
      if (startDateCell == null || startDateCell.value == null) {
        throw Exception('Start date not found in C1');
      }

      final dateValue = startDateCell.value.toString();
      final startDate = DateFormat('yyyy-MM-dd').parse(dateValue);
      final endDate = startDate.add(const Duration(days: 8)).subtract(const Duration(minutes: 1));

      final builder = XmlBuilder();
      builder.processing('xml', 'version="1.0" encoding="UTF-16"');
      builder.element('EmployeeScheduleImport', nest: () {
        builder.element('ScheduleImport', nest: () {
          builder.element('StartTime',
              nest: () => builder.text(DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").format(startDate)));
          builder.element('EndTime',
              nest: () => builder.text(DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").format(endDate)));
          builder.element('DeleteLevel', nest: () => builder.text('ALL'));
          builder.element('ValidationLevel',
              nest: () => builder.text('NONE'));

          for (var row in sheet!.rows.skip(2)) {
            final employeeId = row[3]?.value.toString();
            if (employeeId == null || employeeId.isEmpty) {
              continue;
            }

            for (int i = 5; i <= 11; i++) {
              final cellValue = row[i]?.value.toString();
              if (cellValue == null || cellValue.isEmpty) {
                continue;
              }

              final dayOffset = i - 5;
              final scheduleDate = startDate.add(Duration(days: dayOffset));

              final times = cellValue.split('-');
              if (times.length != 2) {
                continue;
              }

              final startTimeStr = times[0].padLeft(4, '0');
              final endTimeStr = times[1].padLeft(4, '0');

              final startHour = int.parse(startTimeStr.substring(0, 2));
              final startMinute = int.parse(startTimeStr.substring(2, 4));
              final endHour = int.parse(endTimeStr.substring(0, 2));
              final endMinute = int.parse(endTimeStr.substring(2, 4));

              final scheduleStartTime = DateTime(scheduleDate.year, scheduleDate.month, scheduleDate.day, startHour, startMinute);
              final scheduleEndTime = DateTime(scheduleDate.year, scheduleDate.month, scheduleDate.day, endHour, endMinute);

              builder.element('EmployeeSchedule', nest: () {
                builder.element('EmployeeXrefCode',
                    nest: () => builder.text(employeeId));
                builder.element('StartTime',
                    nest: () => builder.text(DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").format(scheduleStartTime)));
                builder.element('EndTime',
                    nest: () => builder.text(DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").format(scheduleEndTime)));
                builder.element('SkillQualifier', nest: () {
                  builder.element('SkillXRefCode',
                      nest: () => builder.text('PAX'));
                  builder.element('IsMandatory',
                      nest: () => builder.text('1'));
                });
              });
            }
          }
        });
      });

      final xmlDocument = builder.buildDocument();
      final xmlString = xmlDocument.toXmlString(pretty: true);
      final bytes = utf8.encode(xmlString);
      await _fileSaver.save(bytes, 'employee_schedule.xml');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('XML file saved successfully!'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('An error occurred: $e'),
          ),
        );
      }
    }
  }

  Future<void> _pickFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['xlsx', 'xlsm'],
      withData: true,
    );

    if (result != null) {
      await _processFile(result.files.first.bytes!);
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Excel to XML Converter'),
      ),
      body: DropRegion(
        formats: Formats.standardFormats,
        onDropOver: (event) {
          setState(() {
            _dragging = true;
          });
          return DropOperation.copy;
        },
        onDropLeave: (event) {
          setState(() {
            _dragging = false;
          });
        },
        onPerformDrop: (event) async {
          setState(() {
            _dragging = false;
          });

          try {
            final item = event.session.items.first;
            final reader = item.dataReader!;

            // Try to handle file data
            bool handled = false;

            // Try file URI first (works on desktop)
            if (reader.canProvide(Formats.fileUri)) {
              reader.getValue<Uri>(Formats.fileUri, (uri) async {
                if (uri != null) {
                  try {
                    if (kIsWeb) {
                      // On web, the URI might be a blob URL, handle differently
                      _showError('Web drag and drop not fully supported. Please use the file picker button.');
                    } else {
                      // On desktop, read file from URI
                      final file = File.fromUri(uri);
                      final bytes = await file.readAsBytes();
                      await _processFile(bytes);
                    }
                  } catch (e) {
                    _showError('Error reading dropped file: $e');
                  }
                }
              }, onError: (error) {
                _showError('Error accessing dropped file: $error');
              });
              handled = true;
            }

            // If not handled, show error message
            if (!handled) {
              if (kIsWeb) {
                _showError('Web drag and drop not fully supported. Please use the file picker button.');
              } else {
                _showError('Dropped item is not a supported file format. Please drop an Excel file (.xlsx or .xlsm).');
              }
            }
          } catch (e) {
            _showError('Error processing dropped item: $e');
          }
        },
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: _dragging ? Colors.blue : Colors.transparent,
              width: 4,
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _pickFile,
                  child: const Text('Pick an Excel File'),
                ),
                const SizedBox(height: 20),
                const Text('or drag and drop a file here'),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
