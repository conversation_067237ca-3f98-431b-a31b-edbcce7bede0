import 'dart:html' as html;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'platform_drop_zone.dart';

class WebDropZone extends StatefulWidget {
  final Widget child;
  final void Function(List<int> bytes) onDrop;

  const WebDropZone({
    Key? key,
    required this.child,
    required this.onDrop,
  }) : super(key: key);

  @override
  _WebDropZoneState createState() => _WebDropZoneState();
}

class _WebDropZoneState extends State<WebDropZone> {
  bool _highlighted = false;

  @override
  void initState() {
    super.initState();
    html.document.body!.onDragOver.listen((event) {
      event.preventDefault();
      setState(() {
        _highlighted = true;
      });
    });
    html.document.body!.onDragLeave.listen((event) {
      event.preventDefault();
      setState(() {
        _highlighted = false;
      });
    });
    html.document.body!.onDrop.listen((event) {
      event.preventDefault();
      setState(() {
        _highlighted = false;
      });
      final files = event.dataTransfer.files;
      if (files != null && files.isNotEmpty) {
        final file = files.first;
        final reader = html.FileReader();
        reader.readAsArrayBuffer(file);
        reader.onLoadEnd.listen((event) {
          widget.onDrop(reader.result as Uint8List);
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: _highlighted ? Colors.blue : Colors.transparent,
          width: 4,
        ),
      ),
      child: widget.child,
    );
  }
}

PlatformDropZone getPlatformDropZone({
  required Widget child,
  required void Function(List<int> bytes) onDrop,
}) =>
    PlatformDropZone(
      child: WebDropZone(
        onDrop: onDrop,
        child: child,
      ),
      onDrop: onDrop,
    );