import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'saver_stub.dart';

class DesktopSaver implements FileSaver {
  @override
  Future<void> save(Uint8List bytes, String fileName) async {
    final downloadsDir = await getDownloadsDirectory();
    final file = File('${downloadsDir!.path}/$fileName');
    await file.writeAsBytes(bytes);
  }
}

FileSaver getSaver() => DesktopSaver();