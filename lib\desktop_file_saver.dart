import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'file_saver.dart';

class DesktopFileSaver implements FileSaver {
  @override
  Future<void> save(Uint8List bytes, String fileName) async {
    final downloadsDir = await getDownloadsDirectory();
    final file = File('${downloadsDir!.path}/$fileName');
    await file.writeAsBytes(bytes);
  }
}

FileSaver getFileSaver() => DesktopFileSaver();